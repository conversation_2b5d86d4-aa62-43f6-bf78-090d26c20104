<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 HNDH Software Technology Co., Ltd.
 * createtime: 2017/12/11 15:08
 */

namespace Dhc\Library\ldpay;

use Exception;
class Pay
{
	const version = '1.0';
	const apiUrl = 'http://sh.ldzfpay.com/apisubmit';
	public $config;
	public function __construct($data)
	{
		$this->setCustomerid($data['merchant_no']);
		$this->setUserkey($data['access_token']);
		$this->setPaytype($data['pay_type']);
		$this->setTotal_fee($data['total_fee'] / 100);
		$this->setSdorderno($data['terminal_trace']);
		$this->setNotifyurl($data['notify_url']);
		$this->setreturnurl($data['notify_url']);
	}

	public function run()
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, self::apiUrl);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $this->getPostData());

		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

		$output = curl_exec($ch);
		$headers =  curl_getinfo($ch);
		curl_close($ch);

		if (empty($output)) {
			return  $headers["redirect_url"];
		} else {
			throw new Exception($output);
		}
	}

	private function getPostData()
	{
		return [
			'version' => self::version,
			'customerid' => $this->config['customerid'],
			'sdorderno' => $this->config['sdorderno'],
			'total_fee' => $this->config['total_fee'],
			'paytype' => $this->config['paytype'],
			'notifyurl' => $this->config['notifyurl'],
			'returnurl' => $this->config['returnurl'],
			'remark' => '在线充值',
			'sign' => $this->getSign(),
		];
	}

	/**
	 * 生成签名
	 * @return string
	 */
	public function getSign()
	{
		return md5('version='.self::version.'&customerid='.$this->config['customerid'].'&total_fee='.$this->config['total_fee'].'&sdorderno='.$this->config['sdorderno'].'&notifyurl='.$this->config['notifyurl'].'&returnurl='.$this->config['returnurl'].'&'.$this->config['userkey']);
	}

	/**
	 * 设置商户号
	 * @param $data
	 * @throws Exception
	 */
	private function setCustomerid($data)
	{
		if (empty($data)) {
			throw new Exception('商户号不能为空');
		}
		$this->config['customerid'] = $data;
	}

	/**
	 * 设置密匙
	 * @param $data
	 * @throws Exception
	 */
	private function setUserkey($data)
	{
		if (empty($data)) {
			throw new Exception('密匙不能为空');
		}
		$this->config['userkey'] = $data;
	}

	/**
	 * 设置异步通知地址
	 * @param $data
	 * @throws Exception
	 */
	private function setNotifyurl($data)
	{
		if (empty($data)) {
			throw new Exception('异步通知地址不能为空');
		}
		$this->config['notifyurl'] = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/Notify/ldpay';
	}

	/**
	 * 设置返回地址
	 * @param $data
	 * @throws Exception
	 */
	private function setreturnurl($data)
	{
		if (empty($data)) {
			throw new Exception('返回地址不能为空');
		}
		$this->config['returnurl'] = $data;
	}

	/**
	 * 设置订单号
	 * @param $data
	 * @throws Exception
	 */
	private function setSdorderno($data)
	{
		if (empty($data)) {
			throw new Exception('订单号不能为空');
		}
		$this->config['sdorderno'] = $data;
	}

	/**
	 * 设置支付类型
	 * @param $data
	 * @throws Exception
	 */
	private function setPaytype($data)
	{
		if ($data == 'wechet') {
			$data = 'wxh5';
		} else {
			$data = 'alipaywap';
		}
		if (empty($data)) {
			throw new Exception('支付类型不能为空');
		}
		$this->config['paytype'] = $data;
	}

	/**
	 * 设置支付金额
	 * @param $data
	 * @throws Exception
	 */
	private function setTotal_fee($data)
	{
		$data = number_format($data,2,'.','');
		if (empty($data) || $data <= 0) {
			throw new Exception('支付金额不能小于0');
		}
		$this->config['total_fee'] = $data;
	}

}
