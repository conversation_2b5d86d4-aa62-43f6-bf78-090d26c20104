#!/usr/bin/env php
<?php

/**
 * Build the whole library into a single file
 * as an easy drop in solution as opposed to
 * relying on autoloader.  Sometimes we just
 * want to hack with an API as a one off thing.
 * Httpful should make this easy.
 */

function exit_unless($condition, $msg = null) {
    if ($condition)
        return;
    echo "[FAIL]\n$msg\n";
    exit(1);
}

// Create the Httpful Phar
echo "Building Phar... ";
$base_dir = dirname(__FILE__);
$source_dir = $base_dir . '/src/Httpful/';
$phar_path = $base_dir . '/downloads/httpful.phar';
$phar = new Phar($phar_path, 0, 'httpful.phar');
$stub = <<<HEREDOC
<?php
    // Phar Stub File
    Phar::mapPhar('httpful.phar');
    include('phar://httpful.phar/Httpful/Bootstrap.php');
    \Httpful\Bootstrap::pharInit();

    __HALT_COMPILER();
HEREDOC;
try {
    $phar->setStub($stub);
} catch(Exception $e) {
    $phar = false;
}
exit_unless($phar, "Unable to create a phar.  Make certain you have phar.readonly=0 set in your ini file.");
$phar->buildFromDirectory(dirname($source_dir));
echo "[ OK ]\n";



// Add it to git!
//echo "Adding httpful.phar to the repo... ";
//$return_code = 0;
//passthru("git add $phar_path", $return_code);
//exit_unless($return_code === 0, "Unable to add download files to git.");
//echo "[ OK ]\n";
echo "\nBuild completed successfully.\n\n";
